[package]
name = "win_tun_main"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
flyshadow_common = { path = "../flyshadow_common" }
tunnel = { path = "../tunnel" }
flyshadow_core_lib = { path = "../flyshadow_core_lib" }

tokio = { version = "1.38.0", features = ["full"] }
serde = "1.0.204"
serde_json = "1.0.120"
trust-dns-resolver = "0.23.2"

android_logger = "0.13.3"
log = "0.4.22"
simple_logger = "4.3.3"

[target.'cfg(target_os = "windows")'.dependencies]
wintun = "0.4.0"
winroute = "0.2.0"
socket2 = "0.5.7"
get_if_addrs = "0.5.3"

[lib]
name = "flyshadow_core_lib"
path = "../flyshadow_core_lib/src/lib.rs"
