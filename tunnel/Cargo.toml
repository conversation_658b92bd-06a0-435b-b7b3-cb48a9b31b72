[package]
name = "tunnel"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
flyshadow_common = { path = "../flyshadow_common" }
tokio = { version = "1", features = ["full"] }

md5 = "0"
regex = "1"
ipnet = "2"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
serde_derive = "1"
trust-dns-resolver = "0"
hashlru = "0"
rand = "0"
trust-dns-proto = "0"
trust-dns-client = "0"
futures = "0"
netstack-lwip = { path = "../../netstack-lwip" }
socket2 = "0"
sysinfo = "0"
crc32fast = "1"
base64 = "0.21"
anyhow = "1"
thiserror = "1"
uuid = { version = "1", features = ["v4"] }
rustls = { version = "0.22", features = [], default-features = false }
tungstenite = "0"
tokio-tungstenite = "0"
futures-util = "0"
libc = "0.2"
geoip2 = "0.1.7"

android_logger = "0"
simple_logger = "5"
hex = "0"
log = "0"


[target.'cfg(target_os = "windows")'.dependencies]
netstat2 = "0.9.1"
